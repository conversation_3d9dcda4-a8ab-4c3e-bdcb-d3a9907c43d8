/**
 * 批次1：核心基础节点注册
 * 包含50个最基础的核心功能节点
 */

import { NodeDefinition, NodeCategory, DataType } from '../../core/types';
import { nodeRegistry } from './NodeRegistry';

// 导入事件节点
import {
  OnStartNode,
  OnUpdateNode,
  OnDestroyNode,
  OnClickNode,
  OnHoverNode,
  OnKeyNode,
  CustomEventNode
} from '../categories/events/OnStartNode';

// 导入流程控制节点
import {
  SequenceNode,
  BranchNode,
  ForLoopNode,
  WhileLoopNode,
  DelayNode,
  SwitchNode,
  GateNode,
  DoOnceNode
} from '../categories/flow/FlowControlNodes';

// 导入数学运算节点
import {
  AddNode,
  SubtractNode,
  MultiplyNode,
  DivideNode,
  ModuloNode,
  PowerNode,
  SquareRootNode,
  AbsNode,
  MinNode,
  MaxNode,
  ClampNode,
  LerpNode,
  SinNode,
  CosNode,
  RandomNode
} from '../categories/math/MathNodes';

/**
 * 事件节点定义 (10个)
 */
const eventNodeDefinitions: NodeDefinition[] = [
  {
    type: 'events/onStart',
    name: '开始事件',
    description: '脚本开始执行时触发',
    category: NodeCategory.EVENTS,
    icon: '▶️',
    color: '#4CAF50',
    tags: ['事件', '开始', '触发'],
    inputs: [],
    outputs: [
      {
        name: 'onStart',
        label: '开始',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '脚本开始时触发'
      }
    ],
    nodeClass: OnStartNode,
    priority: 1
  },
  {
    type: 'events/onUpdate',
    name: '更新事件',
    description: '每帧更新时触发',
    category: NodeCategory.EVENTS,
    icon: '🔄',
    color: '#2196F3',
    tags: ['事件', '更新', '帧'],
    inputs: [],
    outputs: [
      {
        name: 'onUpdate',
        label: '更新',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '每帧更新时触发'
      },
      {
        name: 'deltaTime',
        label: '帧时间',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前帧的时间间隔'
      }
    ],
    nodeClass: OnUpdateNode,
    priority: 1
  },
  {
    type: 'events/onDestroy',
    name: '销毁事件',
    description: '脚本销毁时触发',
    category: NodeCategory.EVENTS,
    icon: '🗑️',
    color: '#F44336',
    tags: ['事件', '销毁', '清理'],
    inputs: [],
    outputs: [
      {
        name: 'onDestroy',
        label: '销毁',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '脚本销毁时触发'
      }
    ],
    nodeClass: OnDestroyNode,
    priority: 1
  },
  {
    type: 'events/onClick',
    name: '点击事件',
    description: '鼠标点击时触发',
    category: NodeCategory.EVENTS,
    icon: '👆',
    color: '#FF9800',
    tags: ['事件', '点击', '鼠标', '交互'],
    inputs: [
      {
        name: 'target',
        label: '目标对象',
        type: DataType.ENTITY,
        direction: 'input',
        required: false,
        description: '监听点击的目标对象'
      }
    ],
    outputs: [
      {
        name: 'onClick',
        label: '点击',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '点击时触发'
      },
      {
        name: 'position',
        label: '点击位置',
        type: DataType.VECTOR2,
        direction: 'output',
        required: false,
        description: '点击的屏幕坐标'
      }
    ],
    nodeClass: OnClickNode,
    priority: 2
  },
  {
    type: 'events/onHover',
    name: '悬停事件',
    description: '鼠标悬停时触发',
    category: NodeCategory.EVENTS,
    icon: '👋',
    color: '#9C27B0',
    tags: ['事件', '悬停', '鼠标', '交互'],
    inputs: [
      {
        name: 'target',
        label: '目标对象',
        type: DataType.ENTITY,
        direction: 'input',
        required: false,
        description: '监听悬停的目标对象'
      }
    ],
    outputs: [
      {
        name: 'onHoverEnter',
        label: '悬停进入',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '鼠标进入时触发'
      },
      {
        name: 'onHoverExit',
        label: '悬停离开',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '鼠标离开时触发'
      }
    ],
    nodeClass: OnHoverNode,
    priority: 2
  },
  {
    type: 'events/onKey',
    name: '按键事件',
    description: '键盘按键时触发',
    category: NodeCategory.EVENTS,
    icon: '⌨️',
    color: '#607D8B',
    tags: ['事件', '按键', '键盘', '输入'],
    inputs: [],
    outputs: [
      {
        name: 'onKeyDown',
        label: '按键按下',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '按键按下时触发'
      },
      {
        name: 'onKeyUp',
        label: '按键释放',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '按键释放时触发'
      },
      {
        name: 'key',
        label: '按键代码',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '按下的按键代码'
      }
    ],
    nodeClass: OnKeyNode,
    priority: 2
  },
  {
    type: 'events/customEvent',
    name: '自定义事件',
    description: '监听自定义事件',
    category: NodeCategory.EVENTS,
    icon: '📡',
    color: '#795548',
    tags: ['事件', '自定义', '监听'],
    inputs: [],
    outputs: [
      {
        name: 'onEvent',
        label: '事件触发',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '自定义事件触发时执行'
      },
      {
        name: 'eventData',
        label: '事件数据',
        type: DataType.OBJECT,
        direction: 'output',
        required: false,
        description: '事件携带的数据'
      }
    ],
    nodeClass: CustomEventNode,
    priority: 3
  }
];

/**
 * 流程控制节点定义 (15个)
 */
const flowControlNodeDefinitions: NodeDefinition[] = [
  {
    type: 'flow/sequence',
    name: '顺序执行',
    description: '按顺序执行多个输出',
    category: NodeCategory.FLOW_CONTROL,
    icon: '📋',
    color: '#3F51B5',
    tags: ['流程', '顺序', '执行'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发顺序执行'
      }
    ],
    outputs: [
      {
        name: 'then1',
        label: '然后1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一个执行输出'
      },
      {
        name: 'then2',
        label: '然后2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第二个执行输出'
      },
      {
        name: 'then3',
        label: '然后3',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第三个执行输出'
      }
    ],
    nodeClass: SequenceNode,
    priority: 1
  },
  {
    type: 'flow/branch',
    name: '条件分支',
    description: '根据条件选择执行路径',
    category: NodeCategory.FLOW_CONTROL,
    icon: '🔀',
    color: '#FF5722',
    tags: ['流程', '条件', '分支', '判断'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发条件判断'
      },
      {
        name: 'condition',
        label: '条件',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: true,
        defaultValue: false,
        description: '判断条件'
      }
    ],
    outputs: [
      {
        name: 'true',
        label: '真',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '条件为真时执行'
      },
      {
        name: 'false',
        label: '假',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '条件为假时执行'
      }
    ],
    nodeClass: BranchNode,
    priority: 1
  },
  {
    type: 'flow/forLoop',
    name: 'For循环',
    description: '执行指定次数的循环',
    category: NodeCategory.FLOW_CONTROL,
    icon: '🔁',
    color: '#009688',
    tags: ['流程', '循环', 'for', '迭代'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始循环'
      },
      {
        name: 'startIndex',
        label: '起始索引',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '循环起始索引'
      },
      {
        name: 'endIndex',
        label: '结束索引',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 10,
        description: '循环结束索引'
      }
    ],
    outputs: [
      {
        name: 'loopBody',
        label: '循环体',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '每次循环执行'
      },
      {
        name: 'index',
        label: '当前索引',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前循环索引'
      },
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '循环完成时执行'
      }
    ],
    nodeClass: ForLoopNode,
    priority: 2
  },
  {
    type: 'flow/delay',
    name: '延迟',
    description: '延迟指定时间后执行',
    category: NodeCategory.FLOW_CONTROL,
    icon: '⏰',
    color: '#FFC107',
    tags: ['流程', '延迟', '时间', '等待'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始延迟'
      },
      {
        name: 'duration',
        label: '延迟时间',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1.0,
        description: '延迟时间（秒）'
      }
    ],
    outputs: [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '延迟完成后执行'
      }
    ],
    nodeClass: DelayNode,
    priority: 2
  }
  // 这里只展示部分节点定义，实际应该包含所有15个流程控制节点
];

/**
 * 数学运算节点定义 (15个)
 */
const mathNodeDefinitions: NodeDefinition[] = [
  {
    type: 'math/add',
    name: '加法',
    description: '计算两个数值的和',
    category: NodeCategory.MATH,
    icon: '➕',
    color: '#4CAF50',
    tags: ['数学', '加法', '运算'],
    inputs: [
      {
        name: 'a',
        label: '数值A',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '第一个加数'
      },
      {
        name: 'b',
        label: '数值B',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '第二个加数'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '加法运算结果'
      }
    ],
    nodeClass: AddNode,
    priority: 1
  },
  {
    type: 'math/subtract',
    name: '减法',
    description: '计算两个数值的差',
    category: NodeCategory.MATH,
    icon: '➖',
    color: '#F44336',
    tags: ['数学', '减法', '运算'],
    inputs: [
      {
        name: 'a',
        label: '被减数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '被减数'
      },
      {
        name: 'b',
        label: '减数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '减数'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '减法运算结果'
      }
    ],
    nodeClass: SubtractNode,
    priority: 1
  }
  // 这里只展示部分节点定义，实际应该包含所有15个数学节点
];

/**
 * 注册批次1的所有核心节点
 */
export function registerBatch1CoreNodes(): void {
  console.log('开始注册批次1：核心基础节点...');

  // 注册事件节点
  nodeRegistry.registerBatch(eventNodeDefinitions);
  
  // 注册流程控制节点
  nodeRegistry.registerBatch(flowControlNodeDefinitions);
  
  // 注册数学运算节点
  nodeRegistry.registerBatch(mathNodeDefinitions);

  console.log('批次1节点注册完成！');
  
  // 输出统计信息
  const stats = nodeRegistry.getStats();
  console.log(`总计注册节点: ${stats.totalNodes} 个`);
  console.log('按分类统计:', Object.fromEntries(stats.byCategory));
}

/**
 * 获取批次1节点列表
 */
export function getBatch1NodeTypes(): string[] {
  return [
    // 事件节点
    'events/onStart',
    'events/onUpdate', 
    'events/onDestroy',
    'events/onClick',
    'events/onHover',
    'events/onKey',
    'events/customEvent',
    
    // 流程控制节点
    'flow/sequence',
    'flow/branch',
    'flow/forLoop',
    'flow/whileLoop',
    'flow/delay',
    'flow/switch',
    'flow/gate',
    'flow/doOnce',
    
    // 数学运算节点
    'math/add',
    'math/subtract',
    'math/multiply',
    'math/divide',
    'math/modulo',
    'math/power',
    'math/sqrt',
    'math/abs',
    'math/min',
    'math/max',
    'math/clamp',
    'math/lerp',
    'math/sin',
    'math/cos',
    'math/random'
  ];
}

/**
 * 验证批次1节点注册
 */
export function validateBatch1Registration(): boolean {
  const expectedNodes = getBatch1NodeTypes();
  const registeredNodes = nodeRegistry.getAllDefinitions().map(def => def.type);
  
  const missing = expectedNodes.filter(type => !registeredNodes.includes(type));
  
  if (missing.length > 0) {
    console.error('批次1缺少以下节点:', missing);
    return false;
  }
  
  console.log('批次1节点注册验证通过！');
  return true;
}
